// App.tsx
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Keyboard,
  TouchableWithoutFeedback,
  StatusBar,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { DualQuickChoicesBar } from './src/components/DualQuickChoicesBar';
import { CurrencyInputs } from './src/components/CurrencyInputs';
import { AppHeader } from './src/components/AppHeader';

const EXCHANGE_RATE = 3.43; // 1 EUR = 3.43 TND

const App: React.FC = () => {
  const [euroAmount, setEuroAmount] = useState<string>('');
  const [dinarAmount, setDinarAmount] = useState<string>('');
  const [activeInput, setActiveInput] = useState<'euro' | 'dinar' | null>(null);
  const [isSourceCurrency, setIsSourceCurrency] = useState<boolean>(true);
  const [error, setError] = useState<string>('');

  const euroQuickChoices = [10, 20, 50, 100, 200, 500];
  const dinarQuickChoices = [30, 70, 170, 340, 680, 1700];

  // Gestion clavier
  useEffect(() => {
    const showSubscription = Keyboard.addListener('keyboardDidShow', () => {
      // Le clavier est affiché
    });

    const hideSubscription = Keyboard.addListener('keyboardDidHide', () => {
      setTimeout(() => {
        setActiveInput(null);
      }, 50);
    });

    return () => {
      showSubscription.remove();
      hideSubscription.remove();
    };
  }, []);

  // Validation des montants
  const validateAmount = (amount: string): boolean => {
    const numericAmount = parseFloat(amount);
    if (isNaN(numericAmount) || numericAmount < 0) {
      setError('Veuillez entrer un montant valide');
      return false;
    }
    if (numericAmount > 10000) {
      setError('Le montant maximum autorisé est de 10 000');
      return false;
    }
    setError('');
    return true;
  };

  // Formatage des nombres avec 2 décimales
  const formatCurrency = (value: number): string => {
    return value.toFixed(2);
  };

  // Convertisseur Euro vers Dinar
  const handleEuroChange = (text: string) => {
    // Validation du format numérique
    const formattedText = text.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');
    setEuroAmount(formattedText);
    setIsSourceCurrency(true);
    
    if (validateAmount(formattedText)) {
      const euroValue = parseFloat(formattedText);
      if (!isNaN(euroValue)) {
        const convertedValue = euroValue * EXCHANGE_RATE;
        setDinarAmount(formatCurrency(convertedValue));
      } else {
        setDinarAmount('');
      }
    }
  };

  // Convertisseur Dinar vers Euro
  const handleDinarChange = (text: string) => {
    // Validation du format numérique
    const formattedText = text.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');
    setDinarAmount(formattedText);
    setIsSourceCurrency(false);
    
    if (validateAmount(formattedText)) {
      const dinarValue = parseFloat(formattedText);
      if (!isNaN(dinarValue)) {
        const convertedValue = dinarValue / EXCHANGE_RATE;
        setEuroAmount(formatCurrency(convertedValue));
      } else {
        setEuroAmount('');
      }
    }
  };

  // Choix rapide Euro
  const handleEuroQuickChoice = (value: number) => {
    const formattedValue = value.toString();
    setEuroAmount(formattedValue);
    setDinarAmount(formatCurrency(value * EXCHANGE_RATE));
    setIsSourceCurrency(true);
    setError('');
    Keyboard.dismiss();
  };

  // Choix rapide Dinar
  const handleDinarQuickChoice = (value: number) => {
    const formattedValue = value.toString();
    setDinarAmount(formattedValue);
    setEuroAmount(formatCurrency(value / EXCHANGE_RATE));
    setIsSourceCurrency(false);
    setError('');
    Keyboard.dismiss();
  };

  // Fonction pour échanger les devises
  const handleSwapCurrencies = () => {
    const tempEuro = euroAmount;
    const tempDinar = dinarAmount;
    setEuroAmount(tempDinar);
    setDinarAmount(tempEuro);
    setIsSourceCurrency(!isSourceCurrency);
  };

  // Fonction pour réinitialiser
  const handleReset = () => {
    setEuroAmount('');
    setDinarAmount('');
    setError('');
    setActiveInput(null);
    Keyboard.dismiss();
  };

  const dismissKeyboard = () => {
    Keyboard.dismiss();
    setActiveInput(null);
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#f8f9fa" />
      
      <AppHeader 
        title="Convertisseur de Devises"
        onReset={handleReset}
        hasValues={!!(euroAmount || dinarAmount)}
      />

      <TouchableWithoutFeedback onPress={dismissKeyboard}>
        <KeyboardAvoidingView
          style={styles.keyboardAvoidingView}
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
          <View style={styles.content}>
            <View style={styles.exchangeRateCard}>
              <Text style={styles.exchangeRateTitle}>Taux de change actuel</Text>
              <Text style={styles.exchangeRate}>1 EUR = {EXCHANGE_RATE} TND</Text>
              <Text style={styles.exchangeRateSubtitle}>Mis à jour il y a quelques instants</Text>
            </View>

            {/* Composant des inputs de devises */}
            <CurrencyInputs
              euroAmount={euroAmount}
              dinarAmount={dinarAmount}
              activeInput={activeInput}
              onEuroChange={handleEuroChange}
              onDinarChange={handleDinarChange}
              setActiveInput={setActiveInput}
              onSwap={handleSwapCurrencies}
              error={error}
              isSourceCurrency={isSourceCurrency}
            />

            {/* Résultat de la conversion */}
            {(euroAmount || dinarAmount) && !error && (
              <View style={styles.resultContainer}>
                <Text style={styles.resultLabel}>Résultat de la conversion :</Text>
                <Text style={styles.resultText}>
                  {isSourceCurrency 
                    ? `${euroAmount} EUR = ${dinarAmount} TND`
                    : `${dinarAmount} TND = ${euroAmount} EUR`
                  }
                </Text>
                {parseFloat(isSourceCurrency ? euroAmount : dinarAmount) > 0 && (
                  <Text style={styles.feeText}>
                    Frais de change : Gratuit pour cette simulation
                  </Text>
                )}
              </View>
            )}

            {/* Affichage des erreurs */}
            {error && (
              <View style={styles.errorContainer}>
                <Text style={styles.errorText}>{error}</Text>
              </View>
            )}
          </View>

          {/* Barre de choix rapides */}
          <DualQuickChoicesBar
            euroChoices={euroQuickChoices}
            dinarChoices={dinarQuickChoices}
            activeInput={activeInput}
            onEuroChoiceSelect={handleEuroQuickChoice}
            onDinarChoiceSelect={handleDinarQuickChoice}
            isVisible={activeInput === 'euro' || activeInput === 'dinar'}
          />
        </KeyboardAvoidingView>
      </TouchableWithoutFeedback>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: { 
    flex: 1, 
    backgroundColor: '#f8f9fa' 
  },
  keyboardAvoidingView: { 
    flex: 1 
  },
  content: { 
    flex: 1, 
    padding: 20, 
    justifyContent: 'center' 
  },
  exchangeRateCard: {
    backgroundColor: '#ffffff',
    padding: 20,
    borderRadius: 12,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  exchangeRateTitle: {
    fontSize: 14,
    color: '#6c757d',
    textAlign: 'center',
    marginBottom: 8,
    fontWeight: '500',
  },
  exchangeRate: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    color: '#007AFF',
    marginBottom: 4,
  },
  exchangeRateSubtitle: {
    fontSize: 12,
    color: '#28a745',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  resultContainer: { 
    backgroundColor: '#e8f4fd', 
    padding: 20, 
    borderRadius: 12, 
    marginTop: 24,
    borderLeftWidth: 4,
    borderLeftColor: '#007AFF',
  },
  resultLabel: {
    fontSize: 14,
    color: '#495057',
    marginBottom: 8,
    fontWeight: '500',
  },
  resultText: { 
    fontSize: 18, 
    textAlign: 'center', 
    color: '#007AFF', 
    fontWeight: '600',
    marginBottom: 8,
  },
  feeText: {
    fontSize: 12,
    textAlign: 'center',
    color: '#28a745',
    fontStyle: 'italic',
  },
  errorContainer: {
    backgroundColor: '#f8d7da',
    padding: 15,
    borderRadius: 8,
    marginTop: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#dc3545',
  },
  errorText: {
    color: '#721c24',
    fontSize: 14,
    textAlign: 'center',
    fontWeight: '500',
  },
});

export default App;