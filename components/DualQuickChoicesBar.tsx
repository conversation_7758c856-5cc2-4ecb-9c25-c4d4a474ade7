// src/components/DualQuickChoicesBar.tsx
import React, { useEffect, useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Animated } from 'react-native';

interface DualQuickChoicesBarProps {
  euroChoices: number[];
  dinarChoices: number[];
  activeInput: 'euro' | 'dinar' | null;
  onEuroChoiceSelect: (value: number) => void;
  onDinarChoiceSelect: (value: number) => void;
  isVisible: boolean;
}

export const DualQuickChoicesBar: React.FC<DualQuickChoicesBarProps> = ({
  euroChoices,
  dinarChoices,
  activeInput,
  onEuroChoiceSelect,
  onDinarChoiceSelect,
  isVisible,
}) => {
  const [animatedValue] = useState(new Animated.Value(0));
  const [shouldRender, setShouldRender] = useState(false);

  useEffect(() => {
    if (isVisible && activeInput) {
      setShouldRender(true);
      Animated.timing(animatedValue, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }).start();
    } else {
      Animated.timing(animatedValue, {
        toValue: 0,
        duration: 150,
        useNativeDriver: true,
      }).start(() => {
        setShouldRender(false);
      });
    }
  }, [isVisible, activeInput, animatedValue]);

  if (!activeInput) return null;


  const choices = activeInput === 'euro' ? euroChoices : dinarChoices;
  const onChoiceSelect = activeInput === 'euro' ? onEuroChoiceSelect : onDinarChoiceSelect;
  const currency = activeInput === 'euro' ? 'EUR' : 'TND';

  const translateY = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [100, 0],
  });

  const opacity = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 1],
  });

  return (
    <Animated.View 
      style={[
        styles.container,
        {
          opacity,
          transform: [{ translateY }],
        },
      ]}
    >
      <Text style={styles.title}>Choix rapides ({currency}) :</Text>
      <View style={styles.row}>
        {choices.map((choice) => (
          <TouchableOpacity
            key={choice}
            style={styles.button}
            onPress={() => onChoiceSelect(choice)}
            activeOpacity={0.7}
          >
            <Text style={styles.text}>{choice}</Text>
          </TouchableOpacity>
        ))}
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    padding: 15,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 5,
  },
  title: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 10,
    textAlign: 'center',
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 10,
    borderRadius: 8,
    minWidth: 50,
    alignItems: 'center',
    shadowColor: '#007AFF',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  text: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 14,
  },
});