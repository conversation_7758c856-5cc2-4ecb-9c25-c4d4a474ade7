// src/components/AppHeader.tsx
import React from 'react';
import { 
  View, 
  Text, 
  TouchableOpacity, 
  StyleSheet,
  StatusBar,
  Platform
} from 'react-native';

interface AppHeaderProps {
  title: string;
  onReset: () => void;
  hasValues: boolean;
}

export const AppHeader: React.FC<AppHeaderProps> = ({
  title,
  onReset,
  hasValues,
}) => {
  return (
    <View style={styles.container}>
      <View style={styles.content}>
        {/* Titre principal */}
        <View style={styles.titleSection}>
          <Text style={styles.title}>{title}</Text>
          <View style={styles.appBadge}>
            <Text style={styles.appBadgeText}>v1.0</Text>
          </View>
        </View>

        {/* Boutons d'action */}
        <View style={styles.actionsSection}>
          {hasValues && (
            <TouchableOpacity
              style={styles.resetButton}
              onPress={onReset}
              activeOpacity={0.7}
            >
              <Text style={styles.resetButtonText}>Effacer</Text>
            </TouchableOpacity>
          )}
          
          <TouchableOpacity
            style={styles.infoButton}
            onPress={() => {
              // Ici vous pourriez ouvrir une modal d'informations
              console.log('Info pressed');
            }}
            activeOpacity={0.7}
          >
            <Text style={styles.infoButtonText}>ℹ</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Barre de statut colorée */}
      <View style={styles.statusBar} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  content: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  titleSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    color: '#212529',
    marginRight: 8,
  },
  appBadge: {
    backgroundColor: '#28a745',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
  appBadgeText: {
    color: '#ffffff',
    fontSize: 10,
    fontWeight: '600',
  },
  actionsSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  resetButton: {
    backgroundColor: '#dc3545',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
  },
  resetButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
  },
  infoButton: {
    backgroundColor: '#6c757d',
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  infoButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  statusBar: {
    height: 3,
    backgroundColor: '#007AFF',
  },
});