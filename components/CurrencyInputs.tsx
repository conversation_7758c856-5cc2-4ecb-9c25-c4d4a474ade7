// src/components/CurrencyInputs.tsx
import React, { useRef } from 'react';
import { View, Text, TextInput, StyleSheet } from 'react-native';

interface CurrencyInputsProps {
  euroAmount: string;
  dinarAmount: string;
  activeInput: 'euro' | 'dinar' | null;
  onEuroChange: (text: string) => void;
  onDinarChange: (text: string) => void;
  setActiveInput: (input: 'euro' | 'dinar' | null) => void;
}

export const CurrencyInputs: React.FC<CurrencyInputsProps> = ({
  euroAmount,
  dinarAmount,
  activeInput,
  onEuroChange,
  onDinarChange,
  setActiveInput,
}) => {
  const euroInputRef = useRef<TextInput>(null);
  const dinarInputRef = useRef<TextInput>(null);

  return (
    <View style={styles.rowInputs}>
      {/* Input Euro */}
      <View style={styles.inputBlock}>
        <Text style={styles.currencyLabel}>EUR</Text>
        <TextInput
          ref={euroInputRef}
          style={[styles.input, activeInput === 'euro' && styles.inputFocused]}
          value={euroAmount}
          onChangeText={onEuroChange}
          onFocus={() => setActiveInput('euro')}
          onBlur={() => setActiveInput(null)}
          placeholder="0.00"
          keyboardType="decimal-pad"
        />
      </View>

      <Text style={styles.arrowText}>⇄</Text>

      {/* Input Dinar */}
      <View style={styles.inputBlock}>
        <Text style={styles.currencyLabel}>TND</Text>
        <TextInput
          ref={dinarInputRef}
          style={[styles.input, activeInput === 'dinar' && styles.inputFocused]}
          value={dinarAmount}
          onChangeText={onDinarChange}
          onFocus={() => setActiveInput('dinar')}
          onBlur={() => setActiveInput(null)}
          placeholder="0.00"
          keyboardType="decimal-pad"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  rowInputs: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' },
  inputBlock: { flex: 1, marginHorizontal: 5 },
  currencyLabel: { fontSize: 14, fontWeight: '600', marginBottom: 6, textAlign: 'center', color: '#333' },
  input: {
    backgroundColor: '#fff',
    borderWidth: 2,
    borderColor: '#ddd',
    borderRadius: 10,
    padding: 12,
    fontSize: 16,
    textAlign: 'center',
  },
  inputFocused: { borderColor: '#007AFF' },
  arrowText: { fontSize: 22, marginHorizontal: 8, color: '#007AFF', fontWeight: 'bold' },
});
